-- 验证数据转换的正确性
-- 比较原始数据和转换后的数据

-- 查看projectallocation数据库中的原始数据（小数形式）
SELECT 
    user_name,
    project_name,
    participation_rate as original_decimal,
    participation_rate * 100 as converted_percentage
FROM projectallocation.project_participation 
WHERE month = '2025-08' 
    AND user_name = '10107814'
ORDER BY project_name;

-- 验证转换逻辑
-- 原始数据：0.40 -> 转换后：40
-- 原始数据：0.10 -> 转换后：10
-- 原始数据：0.05 -> 转换后：5
-- 原始数据：1.00 -> 转换后：100

-- 查看转换示例
SELECT 
    '原始小数形式' as data_type,
    0.40 as value,
    '40%' as meaning
UNION ALL
SELECT 
    '转换后百分数形式' as data_type,
    40 as value,
    '40%' as meaning
UNION ALL
SELECT 
    '原始小数形式' as data_type,
    1.00 as value,
    '100%' as meaning
UNION ALL
SELECT 
    '转换后百分数形式' as data_type,
    100 as value,
    '100%' as meaning;
