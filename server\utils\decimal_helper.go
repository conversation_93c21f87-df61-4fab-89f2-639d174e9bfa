package utils

import (
	"github.com/shopspring/decimal"
)

// DecimalHelper 提供decimal相关的辅助函数
type DecimalHelper struct{}

// Float64ToDecimal 将float64转换为decimal.Decimal指针
func (h *DecimalHelper) Float64ToDecimal(f *float64) *decimal.Decimal {
	if f == nil {
		return nil
	}
	d := decimal.NewFromFloat(*f)
	return &d
}

// DecimalToFloat64 将decimal.Decimal指针转换为float64指针
func (h *DecimalHelper) DecimalToFloat64(d *decimal.Decimal) *float64 {
	if d == nil {
		return nil
	}
	f, _ := d.Float64()
	return &f
}

// StringToDecimal 将字符串转换为decimal.Decimal指针
func (h *DecimalHelper) StringToDecimal(s string) (*decimal.Decimal, error) {
	if s == "" {
		return nil, nil
	}
	d, err := decimal.NewFromString(s)
	if err != nil {
		return nil, err
	}
	return &d, nil
}

// DecimalToString 将decimal.Decimal指针转换为字符串
func (h *DecimalHelper) DecimalToString(d *decimal.Decimal) string {
	if d == nil {
		return ""
	}
	return d.String()
}

// NewDecimalFromFloat 创建新的decimal.Decimal指针
func (h *DecimalHelper) NewDecimalFromFloat(f float64) *decimal.Decimal {
	d := decimal.NewFromFloat(f)
	return &d
}

// NewDecimalFromString 从字符串创建新的decimal.Decimal指针
func (h *DecimalHelper) NewDecimalFromString(s string) (*decimal.Decimal, error) {
	d, err := decimal.NewFromString(s)
	if err != nil {
		return nil, err
	}
	return &d, nil
}

// ZeroDecimal 返回零值的decimal.Decimal指针
func (h *DecimalHelper) ZeroDecimal() *decimal.Decimal {
	d := decimal.Zero
	return &d
}

// IsZero 检查decimal是否为零
func (h *DecimalHelper) IsZero(d *decimal.Decimal) bool {
	if d == nil {
		return true
	}
	return d.IsZero()
}

// Add 两个decimal相加
func (h *DecimalHelper) Add(a, b *decimal.Decimal) *decimal.Decimal {
	if a == nil && b == nil {
		return nil
	}
	if a == nil {
		return b
	}
	if b == nil {
		return a
	}
	result := a.Add(*b)
	return &result
}

// Sub 两个decimal相减
func (h *DecimalHelper) Sub(a, b *decimal.Decimal) *decimal.Decimal {
	if a == nil && b == nil {
		return nil
	}
	if a == nil {
		zero := decimal.Zero
		result := zero.Sub(*b)
		return &result
	}
	if b == nil {
		return a
	}
	result := a.Sub(*b)
	return &result
}

// Compare 比较两个decimal
// 返回值：-1 if a < b, 0 if a == b, 1 if a > b
func (h *DecimalHelper) Compare(a, b *decimal.Decimal) int {
	if a == nil && b == nil {
		return 0
	}
	if a == nil {
		return -1
	}
	if b == nil {
		return 1
	}
	return a.Cmp(*b)
}

// GreaterThan 检查a是否大于b
func (h *DecimalHelper) GreaterThan(a, b *decimal.Decimal) bool {
	return h.Compare(a, b) > 0
}

// LessThan 检查a是否小于b
func (h *DecimalHelper) LessThan(a, b *decimal.Decimal) bool {
	return h.Compare(a, b) < 0
}

// Equal 检查两个decimal是否相等
func (h *DecimalHelper) Equal(a, b *decimal.Decimal) bool {
	return h.Compare(a, b) == 0
}

// 全局实例
var DecimalHelperInstance = &DecimalHelper{}

// 便捷函数
func Float64ToDecimal(f *float64) *decimal.Decimal {
	return DecimalHelperInstance.Float64ToDecimal(f)
}

func DecimalToFloat64(d *decimal.Decimal) *float64 {
	return DecimalHelperInstance.DecimalToFloat64(d)
}

func NewDecimalFromFloat(f float64) *decimal.Decimal {
	return DecimalHelperInstance.NewDecimalFromFloat(f)
}

func ZeroDecimal() *decimal.Decimal {
	return DecimalHelperInstance.ZeroDecimal()
}
