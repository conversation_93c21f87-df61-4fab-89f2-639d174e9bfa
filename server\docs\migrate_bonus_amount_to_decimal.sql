-- 迁移脚本：将奖金金额字段从 FLOAT 改为 DECIMAL
-- 执行日期：请在执行前备份数据库
-- 影响表：department_manager_score

-- 1. 备份现有数据（可选，建议在执行前手动备份整个数据库）
-- CREATE TABLE department_manager_score_backup AS SELECT * FROM department_manager_score;

-- 2. 修改 department_manager_score 表的 bonus_amount 字段类型
-- 注意：DECIMAL(15,2) 表示总共15位数字，其中2位小数
-- 这可以存储最大 999,999,999,999.99 的金额，应该足够大部分业务场景

ALTER TABLE department_manager_score 
MODIFY COLUMN bonus_amount DECIMAL(15,2) COMMENT '奖金金额';

-- 3. 验证数据迁移结果
-- 检查字段类型是否正确修改
DESCRIBE department_manager_score;

-- 4. 检查数据完整性
-- 验证所有记录的 bonus_amount 字段值是否正确
SELECT 
    COUNT(*) as total_records,
    COUNT(bonus_amount) as non_null_bonus_records,
    MIN(bonus_amount) as min_bonus,
    MAX(bonus_amount) as max_bonus,
    AVG(bonus_amount) as avg_bonus
FROM department_manager_score;

-- 5. 检查是否有异常数据
SELECT id, username, bonus_amount 
FROM department_manager_score 
WHERE bonus_amount IS NOT NULL 
ORDER BY bonus_amount DESC 
LIMIT 10;

-- 注意事项：
-- 1. 执行此脚本前请务必备份数据库
-- 2. 建议在测试环境先执行验证
-- 3. 如果表数据量很大，修改字段类型可能需要较长时间
-- 4. 执行期间表会被锁定，建议在业务低峰期执行
-- 5. 如果需要回滚，请使用备份数据恢复
