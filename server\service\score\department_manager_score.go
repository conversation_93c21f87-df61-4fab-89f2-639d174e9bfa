package score

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/score"
	scoreReq "github.com/flipped-aurora/gin-vue-admin/server/model/score/request"
	"github.com/shopspring/decimal"
)

type DepartmentManagerScoreService struct{}

// CreateDepartmentManagerScore 创建部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) CreateDepartmentManagerScore(ctx context.Context, departmentManagerScore *score.DepartmentManagerScore) (err error) {
	err = global.GVA_DB.Create(departmentManagerScore).Error
	return err
}

// DeleteDepartmentManagerScore 删除部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) DeleteDepartmentManagerScore(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&score.DepartmentManagerScore{}, "id = ?", id).Error
	return err
}

// DeleteDepartmentManagerScoreByIds 批量删除部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) DeleteDepartmentManagerScoreByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]score.DepartmentManagerScore{}, "id in ?", ids).Error
	return err
}

// UpdateDepartmentManagerScore 更新部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) UpdateDepartmentManagerScore(ctx context.Context, departmentManagerScore score.DepartmentManagerScore) (err error) {
	err = global.GVA_DB.Model(&score.DepartmentManagerScore{}).Where("id = ?", departmentManagerScore.Id).Updates(&departmentManagerScore).Error
	return err
}

// GetDepartmentManagerScore 根据id获取部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) GetDepartmentManagerScore(ctx context.Context, id string) (departmentManagerScore score.DepartmentManagerScore, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&departmentManagerScore).Error
	return
}

// GetDepartmentManagerScoreInfoList 分页获取部门/机构负责人评分记录
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) GetDepartmentManagerScoreInfoList(ctx context.Context, info scoreReq.DepartmentManagerScoreSearch) (list []score.DepartmentManagerScore, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&score.DepartmentManagerScore{})
	var departmentManagerScores []score.DepartmentManagerScore
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&departmentManagerScores).Error
	return departmentManagerScores, total, err
}
func (departmentManagerScoreService *DepartmentManagerScoreService) GetDepartmentManagerScorePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// BatchSubmitDepartmentManagerScores 批量提交部门负责人评分
// Author [yourname](https://github.com/yourname)
func (departmentManagerScoreService *DepartmentManagerScoreService) BatchSubmitDepartmentManagerScores(ctx context.Context, requests []scoreReq.BatchSubmitDepartmentManagerScoresRequest) (err error) {
	// 开启事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // 重新抛出panic
		}
	}()

	if err := tx.Error; err != nil {
		return err
	}

	// 1. 获取考核配置信息
	if len(requests) == 0 {
		return nil
	}

	// 使用第一条记录的考核配置ID
	assessmentConfigId := requests[0].AssessmentConfigId
	departmentId := requests[0].DepartmentId
	scorerUsername := requests[0].ScorerUsername

	// 1.5. 删除当前评分者对这些员工的现有记录（避免重复和数据不一致）
	usernames := make([]string, len(requests))
	for i, req := range requests {
		usernames[i] = req.Username
	}

	err = tx.Where("assessment_config_id = ? AND username IN ? AND scorer_username = ? AND department_id = ?",
		assessmentConfigId, usernames, scorerUsername, departmentId).
		Delete(&score.DepartmentManagerScore{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 2. 高分配额验证和更新
	var highScoreCount int
	for _, req := range requests {
		if req.ManagerScore != nil && *req.ManagerScore >= 95 {
			highScoreCount++
		}
	}

	if highScoreCount > 0 {
		// 查询配额信息
		var quotaInfo struct {
			ID               uint   `json:"id"`
			QuotaName        string `json:"quotaName"`
			DepartmentQuotas string `json:"departmentQuotas"` // JSON字符串
		}

		// 查询考核配置关联的配额ID
		var assessmentConfig struct {
			ScoreQuotaID uint `json:"score_quota_id"`
		}
		if err := tx.Table("assessment_config").
			Select("score_quota_id").
			Where("id = ?", assessmentConfigId).
			First(&assessmentConfig).Error; err != nil {
			tx.Rollback()
			return err
		}

		if assessmentConfig.ScoreQuotaID > 0 {
			// 查询配额信息
			if err := tx.Table("score_quota_management").
				Select("id, quota_name, department_quotas").
				Where("id = ?", assessmentConfig.ScoreQuotaID).
				First(&quotaInfo).Error; err != nil {
				tx.Rollback()
				return err
			}

			// 解析JSON并更新配额
			var departmentQuotas []map[string]interface{}
			if err := json.Unmarshal([]byte(quotaInfo.DepartmentQuotas), &departmentQuotas); err != nil {
				tx.Rollback()
				return err
			}

			// 查找对应部门的配额
			var departmentQuota map[string]interface{}
			for i, quota := range departmentQuotas {
				if int(quota["departmentId"].(float64)) == departmentId {
					departmentQuota = departmentQuotas[i]
					break
				}
			}

			if departmentQuota != nil {
				// 计算新的已使用配额
				usedAmount := int(departmentQuota["usedAmount"].(float64)) + highScoreCount
				quotaAmount := int(departmentQuota["quotaAmount"].(float64))

				// 验证配额是否足够
				if usedAmount > quotaAmount {
					tx.Rollback()
					return fmt.Errorf("高分配额不足，当前剩余: %d, 需要: %d", quotaAmount-int(departmentQuota["usedAmount"].(float64)), highScoreCount)
				}

				// 更新配额
				departmentQuota["usedAmount"] = usedAmount
				departmentQuota["remainingAmount"] = quotaAmount - usedAmount

				// 更新JSON
				for i, quota := range departmentQuotas {
					if int(quota["departmentId"].(float64)) == departmentId {
						departmentQuotas[i] = departmentQuota
						break
					}
				}

				// 序列化回JSON
				updatedJSON, err := json.Marshal(departmentQuotas)
				if err != nil {
					tx.Rollback()
					return err
				}

				// 更新数据库
				if err := tx.Table("score_quota_management").
					Where("id = ?", assessmentConfig.ScoreQuotaID).
					Update("department_quotas", string(updatedJSON)).Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	// 3. 奖金额度验证和更新
	totalBonusAmount := decimal.Zero
	for _, req := range requests {
		if req.BonusAmount != nil {
			totalBonusAmount = totalBonusAmount.Add(*req.BonusAmount)
		}
	}

	if totalBonusAmount.GreaterThan(decimal.Zero) {
		// 查询奖金信息
		var bonusInfo struct {
			ID                    uint   `json:"id"`
			BonusName             string `json:"bonusName"`
			DepartmentAllocations string `json:"departmentAllocations"` // JSON字符串
		}

		// 查询考核配置关联的奖金ID
		var assessmentConfig struct {
			BonusRelationID uint `json:"bonus_relation_id"`
		}
		if err := tx.Table("assessment_config").
			Select("bonus_relation_id").
			Where("id = ?", assessmentConfigId).
			First(&assessmentConfig).Error; err != nil {
			tx.Rollback()
			return err
		}

		if assessmentConfig.BonusRelationID > 0 {
			// 查询奖金信息
			if err := tx.Table("bonus_management").
				Select("id, bonus_name, department_allocations").
				Where("id = ?", assessmentConfig.BonusRelationID).
				First(&bonusInfo).Error; err != nil {
				tx.Rollback()
				return err
			}

			// 解析JSON并更新奖金
			var departmentAllocations []map[string]interface{}
			if err := json.Unmarshal([]byte(bonusInfo.DepartmentAllocations), &departmentAllocations); err != nil {
				tx.Rollback()
				return err
			}

			// 查找对应部门的奖金
			var departmentAllocation map[string]interface{}
			for i, allocation := range departmentAllocations {
				if int(allocation["departmentId"].(float64)) == departmentId {
					departmentAllocation = departmentAllocations[i]
					break
				}
			}

			if departmentAllocation != nil {
				allocatedAmount := decimal.NewFromFloat(departmentAllocation["allocatedAmount"].(float64))

				// 重新统计该部门在当前考核配置下的实际奖金使用量（包括其他评分者的分配）
				var actualUsedAmountStr string
				err = tx.Table("department_manager_score").
					Select("COALESCE(SUM(bonus_amount), 0)").
					Where("assessment_config_id = ? AND department_id = ?", assessmentConfigId, departmentId).
					Scan(&actualUsedAmountStr).Error
				if err != nil {
					tx.Rollback()
					return err
				}

				actualUsedAmount, err := decimal.NewFromString(actualUsedAmountStr)
				if err != nil {
					actualUsedAmount = decimal.Zero
				}

				// 计算提交后的总使用量
				newTotalUsedAmount := actualUsedAmount.Add(totalBonusAmount)

				// 验证奖金是否足够
				if newTotalUsedAmount.GreaterThan(allocatedAmount) {
					tx.Rollback()
					remaining := allocatedAmount.Sub(actualUsedAmount)
					return fmt.Errorf("奖金额度不足，当前剩余: %s, 需要: %s",
						remaining.String(), totalBonusAmount.String())
				}

				// 更新奖金分配信息（暂时使用预期值，实际值在插入数据后重新计算）
				usedAmountFloat, _ := newTotalUsedAmount.Float64()
				remainingAmountFloat, _ := allocatedAmount.Sub(newTotalUsedAmount).Float64()
				departmentAllocation["usedAmount"] = usedAmountFloat
				departmentAllocation["remainingAmount"] = remainingAmountFloat

				// 更新JSON
				for i, allocation := range departmentAllocations {
					if int(allocation["departmentId"].(float64)) == departmentId {
						departmentAllocations[i] = departmentAllocation
						break
					}
				}

				// 序列化回JSON
				updatedJSON, err := json.Marshal(departmentAllocations)
				if err != nil {
					tx.Rollback()
					return err
				}

				// 更新数据库
				if err := tx.Table("bonus_management").
					Where("id = ?", assessmentConfig.BonusRelationID).
					Update("department_allocations", string(updatedJSON)).Error; err != nil {
					tx.Rollback()
					return err
				}
			}
		}
	}

	// 4. 插入评分数据
	for _, req := range requests {
		// 构建评分记录
		departmentManagerScore := score.DepartmentManagerScore{
			AssessmentConfigId:   &req.AssessmentConfigId,
			Username:             &req.Username,
			DepartmentId:         &req.DepartmentId,
			ManagerScore:         req.ManagerScore,
			BonusAmount:          req.BonusAmount, // 现在都是decimal.Decimal类型，可以直接赋值
			ScorerUsername:       &req.ScorerUsername,
			CalculationParameter: &req.CalculationParameter,
		}

		// 使用UPSERT操作
		if err := tx.Exec(`
			INSERT INTO department_manager_score
			(assessment_config_id, username, department_id, manager_score, bonus_amount, scorer_username, calculation_parameter)
			VALUES (?, ?, ?, ?, ?, ?, ?)
			ON DUPLICATE KEY UPDATE
			manager_score = VALUES(manager_score),
			bonus_amount = VALUES(bonus_amount),
			scorer_username = VALUES(scorer_username),
			calculation_parameter = VALUES(calculation_parameter),
			updated_at = NOW()
		`,
			departmentManagerScore.AssessmentConfigId,
			departmentManagerScore.Username,
			departmentManagerScore.DepartmentId,
			departmentManagerScore.ManagerScore,
			departmentManagerScore.BonusAmount,
			departmentManagerScore.ScorerUsername,
			departmentManagerScore.CalculationParameter).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 5. 插入数据后，重新统计并更新奖金分配信息（确保数据准确性）
	if totalBonusAmount.GreaterThan(decimal.Zero) {
		// 查询考核配置关联的奖金ID
		var assessmentConfig struct {
			BonusRelationID uint `json:"bonus_relation_id"`
		}
		if err := tx.Table("assessment_config").
			Select("bonus_relation_id").
			Where("id = ?", assessmentConfigId).
			First(&assessmentConfig).Error; err != nil {
			tx.Rollback()
			return err
		}

		if assessmentConfig.BonusRelationID > 0 {
			// 重新统计该部门在当前考核配置下的实际奖金使用量
			var finalUsedAmountStr string
			err = tx.Table("department_manager_score").
				Select("COALESCE(SUM(bonus_amount), 0)").
				Where("assessment_config_id = ? AND department_id = ?", assessmentConfigId, departmentId).
				Scan(&finalUsedAmountStr).Error
			if err != nil {
				tx.Rollback()
				return err
			}

			finalUsedAmount, err := decimal.NewFromString(finalUsedAmountStr)
			if err != nil {
				finalUsedAmount = decimal.Zero
			}

			// 查询并更新奖金管理表
			var bonusInfo struct {
				ID                    uint   `json:"id"`
				BonusName             string `json:"bonusName"`
				DepartmentAllocations string `json:"departmentAllocations"`
			}

			if err := tx.Table("bonus_management").
				Select("id, bonus_name, department_allocations").
				Where("id = ?", assessmentConfig.BonusRelationID).
				First(&bonusInfo).Error; err != nil {
				tx.Rollback()
				return err
			}

			// 解析并更新JSON
			var departmentAllocations []map[string]interface{}
			if err := json.Unmarshal([]byte(bonusInfo.DepartmentAllocations), &departmentAllocations); err != nil {
				tx.Rollback()
				return err
			}

			// 更新对应部门的实际使用量
			for i, allocation := range departmentAllocations {
				if int(allocation["departmentId"].(float64)) == departmentId {
					allocatedAmount := decimal.NewFromFloat(allocation["allocatedAmount"].(float64))
					finalUsedAmountFloat, _ := finalUsedAmount.Float64()
					remainingAmountFloat, _ := allocatedAmount.Sub(finalUsedAmount).Float64()
					departmentAllocations[i]["usedAmount"] = finalUsedAmountFloat
					departmentAllocations[i]["remainingAmount"] = remainingAmountFloat
					break
				}
			}

			// 序列化并更新数据库
			updatedJSON, err := json.Marshal(departmentAllocations)
			if err != nil {
				tx.Rollback()
				return err
			}

			if err := tx.Table("bonus_management").
				Where("id = ?", assessmentConfig.BonusRelationID).
				Update("department_allocations", string(updatedJSON)).Error; err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	return tx.Commit().Error
}
