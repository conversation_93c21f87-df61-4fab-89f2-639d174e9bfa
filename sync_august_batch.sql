-- 批量同步projectallocation数据库中8月份项目参与度数据到examine数据库
-- 使用项目名称匹配的方式进行同步

-- 创建临时映射表
CREATE TEMPORARY TABLE temp_project_mapping AS
SELECT 
    pa.user_name,
    pa.project_name,
    pa.participation_rate,
    ex.id as project_id
FROM projectallocation.project_participation pa
JOIN examine.project_info ex ON pa.project_name = ex.name
WHERE pa.month = '2025-08';

-- 验证映射结果
SELECT 
    COUNT(*) as mapped_records,
    COUNT(DISTINCT user_name) as unique_users,
    COUNT(DISTINCT project_id) as unique_projects
FROM temp_project_mapping;

-- 显示未映射的项目
SELECT DISTINCT project_name 
FROM projectallocation.project_participation 
WHERE month = '2025-08' 
AND project_name NOT IN (
    SELECT name FROM examine.project_info
);

-- 执行同步（需要手动执行）
-- 注意：将小数形式的参与度转换为百分数形式（乘以100）
-- INSERT INTO examine.assessment_coefficient_allocation
-- (assessment_config_id, username, project_id, assessment_coefficient, calculation_parameter, created_at, updated_at)
-- SELECT
--     16 as assessment_config_id,
--     user_name as username,
--     project_id,
--     participation_rate * 100 as assessment_coefficient,  -- 乘以100转换为百分数
--     'project_participation' as calculation_parameter,
--     NOW() as created_at,
--     NOW() as updated_at
-- FROM temp_project_mapping;
